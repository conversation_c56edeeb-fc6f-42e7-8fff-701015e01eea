import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import axios from 'axios';

@Injectable()
export class DevdropService {
  private readonly logger = new Logger(DevdropService.name);

  private readonly RPC_URL = 'https://api.devnet.solana.com/';
  private readonly DROP_PARAMS = {
    id: '8',
    jsonrpc: '2.0',
    method: 'requestAirdrop',
    params: [
      'MNG3SoboXMyjse4ggiyBWJreNhfxyni5VJFxSLmXM5n',
      5000000000,
      { commitment: 'confirmed' },
    ],
  };

  @Cron(CronExpression.EVERY_75_MINUTES)
  async handleAirdrop() {
    this.logger.log('Starting scheduled airdrop request...');
    try {
      const response = await axios.post(this.RPC_URL, this.DROP_PARAMS, {
        headers: {
          accept: 'application/json',
          'content-type': 'application/json; charset=utf-8',
          'solana-client': 'js/2.3.0',
          origin: 'http://localhost:3000',
        },
      });

      this.logger.log('Airdrop response: ' + JSON.stringify(response.data));
    } catch (error) {
      this.logger.error('Error requesting airdrop:', error);
    }
  }
}
